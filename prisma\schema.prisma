generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Model {
  id        String   @id @default(uuid())
  name      String
  thumbnail String?
  fileType  String?
  size      String?
  createdAt DateTime @default(now())
  filePath  String
  url       String   @default("")
}

model Material {
  id        String   @id @default(uuid())
  name      String
  thumbnail String?
  color     String
  metalness Float    @default(0)
  roughness Float    @default(0.5)
  glass     Float    @default(0)
  createdAt DateTime @default(now())
}
