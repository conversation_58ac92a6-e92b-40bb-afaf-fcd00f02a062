// 移除Vercel Blob依赖，使用本地文件上传

export interface RawModelData {
  id: string;
  name: string;
  thumbnail: string | null;
  fileType: string | null;
  size: string | null;
  createdAt: string;
  filePath: string; // This is the pathname in blob storage
  url: string;      // This is the full URL to the blob
}

export interface ModelData {
  id: string;
  name: string;
  thumbnail: string;
  fileType: string;
  size: number;
  createdAt: string;
  filePath: string; // This will be the full URL
}

export interface MaterialData {
  id: string;
  name: string;
  thumbnail: string | null;
  color: string;
  metalness: number;
  roughness: number;
  glass: number;
  createdAt: string;
}

// Use relative path for production, absolute for development
const BASE_URL = '/api';

// Unified error handling for API requests
const handleApiResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: `HTTP error! status: ${response.status}` }));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }
  // For 204 No Content, response.json() will fail, so we return a placeholder.
  if (response.status === 204) {
    return {} as T;
  }
  return response.json();
};

// Transform model data from API to frontend format
const transformModelData = (model: RawModelData): ModelData => {
  return {
    ...model,
    size: model.size ? parseInt(model.size, 10) : 0,
    thumbnail: model.thumbnail || '', // Use the full URL from blob, or empty string
    filePath: model.url, // Use the full URL from blob
    fileType: model.fileType || 'GLB',
  };
};

// Transform material data from API to frontend format
const transformMaterialData = (material: MaterialData): MaterialData => {
    return {
        ...material,
        thumbnail: material.thumbnail || '',
    };
}

class ApiService {
  // --- Uploader ---
  async uploadFile(file: File, onUploadProgress?: (progress: number) => void): Promise<string> {
    try {
      const formData = new FormData();

      // 根据文件类型确定字段名
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      let fieldName = 'thumbnail'; // 默认为缩略图

      if (fileExtension && ['glb', 'gltf'].includes(fileExtension)) {
        fieldName = 'model';
      } else if (fileExtension && ['jpg', 'jpeg', 'png', 'webp'].includes(fileExtension)) {
        fieldName = 'thumbnail';
      }

      formData.append(fieldName, file);

      const xhr = new XMLHttpRequest();

      return new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable && onUploadProgress) {
            const progress = (event.loaded / event.total) * 100;
            onUploadProgress(progress);
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            // 根据字段名返回对应的URL
            const url = response[`${fieldName}Url`] || response.modelUrl || response.thumbnailUrl;
            if (url) {
              resolve(url);
            } else {
              reject(new Error('上传响应中未找到文件URL'));
            }
          } else {
            reject(new Error(`上传失败: ${xhr.status}`));
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error('网络错误，上传失败'));
        });

        xhr.open('POST', `${BASE_URL}/upload`);
        xhr.send(formData);
      });
    } catch (error) {
      console.error('Upload failed:', error);
      throw new Error('文件上传失败，请检查网络连接或联系管理员。');
    }
  }

  // --- Model Methods ---
  async getModels(): Promise<ModelData[]> {
    try {
      const response = await fetch(`${BASE_URL}/models`);
      const models = await handleApiResponse<RawModelData[]>(response);
      return models.map(transformModelData);
    } catch (error) {
      console.error('Failed to fetch models:', error);
      return [];
    }
  }

  async getModel(id: string): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`);
      const model = await handleApiResponse<RawModelData>(response);
      return transformModelData(model);
    } catch (error) {
      console.error(`Failed to get model ${id}:`, error);
      return null;
    }
  }

  async addModel(modelData: {
    name: string;
    fileType: string;
    size: string;
    url: string;
    thumbnailUrl?: string | null;
  }): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(modelData),
      });
      const newModel = await handleApiResponse<RawModelData>(response);
      return transformModelData(newModel);
    } catch (error) {
      console.error('Failed to add model:', error);
      return null;
    }
  }
  
  async updateModel(id: string, name: string, thumbnailUrl?: string | null): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, thumbnailUrl }),
      });
      const updatedModel = await handleApiResponse<RawModelData>(response);
      return transformModelData(updatedModel);
    } catch (error) {
      console.error(`Failed to update model ${id}:`, error);
      return null;
    }
  }

  async deleteModel(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`, { method: 'DELETE' });
      await handleApiResponse(response);
      return true;
    } catch (error) {
      console.error(`Failed to delete model ${id}:`, error);
      return false;
    }
  }

  // --- Material Methods ---
  async getMaterials(): Promise<MaterialData[]> {
    try {
      const response = await fetch(`${BASE_URL}/materials`);
      const materials = await handleApiResponse<MaterialData[]>(response);
      return materials.map(transformMaterialData);
    } catch (error) {
      console.error('Failed to fetch materials:', error);
      return [];
    }
  }

  async addMaterial(materialData: {
    name: string;
    color: string;
    metalness: number;
    roughness: number;
    glass: number;
    thumbnailUrl?: string | null;
  }): Promise<MaterialData | null> {
    try {
      const response = await fetch(`${BASE_URL}/materials`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(materialData),
      });
      const newMaterial = await handleApiResponse<MaterialData>(response);
      return transformMaterialData(newMaterial);
    } catch (error) {
      console.error('Failed to add material:', error);
      return null;
    }
  }

  async deleteMaterial(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${BASE_URL}/materials/${id}`, { method: 'DELETE' });
      await handleApiResponse(response);
      return true;
    } catch (error) {
      console.error(`Failed to delete material ${id}:`, error);
      return false;
    }
  }
}

// Export a single instance of the service
export const apiService = new ApiService();