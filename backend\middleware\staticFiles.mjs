import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置静态文件服务
export const setupStaticFiles = (app) => {
    const uploadDir = process.env.UPLOAD_DIR || path.join(__dirname, '../../uploads');
    
    // 提供静态文件访问
    app.use('/uploads', express.static(uploadDir, {
        maxAge: '1d', // 缓存1天
        etag: true,
        lastModified: true
    }));
    
    console.log(`静态文件服务已启动，目录: ${uploadDir}`);
};
