version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: huitong-postgres
    environment:
      POSTGRES_DB: huitong_material
      POSTGRES_USER: huitong_user
      POSTGRES_PASSWORD: huitong_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - huitong-network
    restart: unless-stopped

  # 应用程序
  app:
    build: .
    container_name: huitong-app
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************************/huitong_material
      PORT: 3000
      UPLOAD_DIR: /app/uploads
      MAX_FILE_SIZE: 50MB
    volumes:
      - uploads_data:/app/uploads
    ports:
      - "3000:3000"
    depends_on:
      - postgres
    networks:
      - huitong-network
    restart: unless-stopped

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: huitong-nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - uploads_data:/var/www/uploads:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - app
    networks:
      - huitong-network
    restart: unless-stopped

volumes:
  postgres_data:
  uploads_data:

networks:
  huitong-network:
    driver: bridge
