#!/usr/bin/env node

/**
 * API测试脚本
 * 用于验证迁移后的API端点是否正常工作
 */

import fetch from 'node-fetch';
global.FormData = FormData;
global.Blob = Blob;
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BASE_URL = 'http://localhost:3001';
const API_URL = `${BASE_URL}/api`;

// 颜色输出
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试函数
async function testHealthCheck() {
    log('\n🏥 测试健康检查端点...', 'blue');
    try {
        const response = await fetch(`${API_URL}/health`);
        const data = await response.json();
        
        if (response.ok && data.status === 'healthy') {
            log('✅ 健康检查通过', 'green');
            return true;
        } else {
            log('❌ 健康检查失败', 'red');
            console.log(data);
            return false;
        }
    } catch (error) {
        log(`❌ 健康检查错误: ${error.message}`, 'red');
        return false;
    }
}

async function testGetModels() {
    log('\n📦 测试获取模型列表...', 'blue');
    try {
        const response = await fetch(`${API_URL}/models`);
        const data = await response.json();
        
        if (response.ok && Array.isArray(data)) {
            log(`✅ 成功获取模型列表 (${data.length} 个模型)`, 'green');
            return true;
        } else {
            log('❌ 获取模型列表失败', 'red');
            console.log(data);
            return false;
        }
    } catch (error) {
        log(`❌ 获取模型列表错误: ${error.message}`, 'red');
        return false;
    }
}

async function testGetMaterials() {
    log('\n🎨 测试获取材质列表...', 'blue');
    try {
        const response = await fetch(`${API_URL}/materials`);
        const data = await response.json();
        
        if (response.ok && Array.isArray(data)) {
            log(`✅ 成功获取材质列表 (${data.length} 个材质)`, 'green');
            return true;
        } else {
            log('❌ 获取材质列表失败', 'red');
            console.log(data);
            return false;
        }
    } catch (error) {
        log(`❌ 获取材质列表错误: ${error.message}`, 'red');
        return false;
    }
}

async function testStaticFiles() {
    log('\n📁 测试静态文件服务...', 'blue');
    try {
        // 检查uploads目录是否可访问
        const response = await fetch(`${BASE_URL}/uploads/`);
        
        if (response.status === 403 || response.status === 404) {
            log('✅ 静态文件服务正常 (目录保护正常)', 'green');
            return true;
        } else if (response.ok) {
            log('✅ 静态文件服务正常', 'green');
            return true;
        } else {
            log('❌ 静态文件服务异常', 'red');
            return false;
        }
    } catch (error) {
        log(`❌ 静态文件服务错误: ${error.message}`, 'red');
        return false;
    }
}

async function testUploadEndpoint() {
    log('\n📤 测试文件上传端点...', 'blue');
    try {
        // 创建一个测试文件
        const testContent = 'test file content';
        const formData = new FormData();
        const blob = new Blob([testContent], { type: 'text/plain' });
        formData.append('thumbnail', blob, 'test.txt');
        
        const response = await fetch(`${API_URL}/upload`, {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const data = await response.json();
            log('✅ 文件上传端点正常', 'green');
            return true;
        } else {
            log('❌ 文件上传端点失败', 'red');
            const errorText = await response.text();
            console.log(errorText);
            return false;
        }
    } catch (error) {
        log(`❌ 文件上传端点错误: ${error.message}`, 'red');
        return false;
    }
}

// 主测试函数
async function runTests() {
    log('🚀 开始API测试...', 'yellow');
    
    const tests = [
        testHealthCheck,
        testGetModels,
        testGetMaterials,
        testStaticFiles,
        testUploadEndpoint
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        const result = await test();
        if (result) {
            passed++;
        } else {
            failed++;
        }
    }
    
    log('\n📊 测试结果:', 'yellow');
    log(`✅ 通过: ${passed}`, 'green');
    log(`❌ 失败: ${failed}`, 'red');
    log(`📈 总计: ${passed + failed}`, 'blue');
    
    if (failed === 0) {
        log('\n🎉 所有测试通过！API迁移成功！', 'green');
        process.exit(0);
    } else {
        log('\n⚠️  部分测试失败，请检查服务器状态', 'yellow');
        process.exit(1);
    }
}

// 检查服务器是否运行
async function checkServerRunning() {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch(`${BASE_URL}/api/health`, {
            signal: controller.signal
        });

        clearTimeout(timeoutId);
        return response.ok;
    } catch (error) {
        return false;
    }
}

// 启动测试
async function main() {
    log('🔍 检查服务器状态...', 'blue');
    
    const isRunning = await checkServerRunning();
    if (!isRunning) {
        log('❌ 服务器未运行，请先启动服务器:', 'red');
        log('   npm run dev', 'yellow');
        log('   或', 'yellow');
        log('   npm start', 'yellow');
        process.exit(1);
    }
    
    await runTests();
}

main().catch(error => {
    log(`💥 测试脚本错误: ${error.message}`, 'red');
    process.exit(1);
});
