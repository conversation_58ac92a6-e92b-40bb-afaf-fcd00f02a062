import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { v4 as uuidv4 } from 'uuid';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 确保上传目录存在
const uploadDir = process.env.UPLOAD_DIR || path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// 创建子目录
const modelsDir = path.join(uploadDir, 'models');
const thumbnailsDir = path.join(uploadDir, 'thumbnails');
const materialsDir = path.join(uploadDir, 'materials');

[modelsDir, thumbnailsDir, materialsDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
});

// 配置存储
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        let uploadPath = uploadDir;
        
        // 根据文件类型选择目录
        if (file.fieldname === 'model') {
            uploadPath = modelsDir;
        } else if (file.fieldname === 'thumbnail') {
            uploadPath = thumbnailsDir;
        } else if (file.fieldname === 'material') {
            uploadPath = materialsDir;
        }
        
        cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
        // 生成唯一文件名
        const uniqueId = uuidv4();
        const ext = path.extname(file.originalname);
        const filename = `${uniqueId}${ext}`;
        cb(null, filename);
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    const allowedTypes = {
        'model': ['.glb', '.gltf'],
        'thumbnail': ['.jpg', '.jpeg', '.png', '.webp'],
        'material': ['.jpg', '.jpeg', '.png', '.webp']
    };
    
    const ext = path.extname(file.originalname).toLowerCase();
    const fieldType = file.fieldname;
    
    if (allowedTypes[fieldType] && allowedTypes[fieldType].includes(ext)) {
        cb(null, true);
    } else {
        cb(new Error(`不支持的文件类型: ${ext}`), false);
    }
};

// 配置multer
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE) || 50 * 1024 * 1024 // 默认50MB
    }
});

// 生成文件URL
export const generateFileUrl = (filename, type = 'models') => {
    const baseUrl = process.env.VITE_SERVER_URL || 'http://localhost:3001';
    return `${baseUrl}/uploads/${type}/${filename}`;
};

// 删除文件
export const deleteFile = (filePath) => {
    try {
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            return true;
        }
        return false;
    } catch (error) {
        console.error('删除文件失败:', error);
        return false;
    }
};

// 从URL提取文件路径
export const getFilePathFromUrl = (url) => {
    if (!url) return null;
    
    // 如果是本地URL，提取文件路径
    if (url.includes('/uploads/')) {
        const urlPath = url.split('/uploads/')[1];
        return path.join(uploadDir, urlPath);
    }
    
    return null;
};

export { upload };
