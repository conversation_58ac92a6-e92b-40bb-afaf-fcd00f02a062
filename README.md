# Huitong Material - 3D材质管理系统

一个基于React + Three.js的3D模型材质管理和预览系统，支持本地部署和阿里云部署。

## 🚀 功能特性

- **3D模型管理**: 支持GLB/GLTF格式的3D模型上传、预览和管理
- **材质系统**: 创建和管理各种材质属性（金属度、粗糙度、玻璃效果等）
- **实时预览**: 基于Three.js的实时3D渲染和材质预览
- **文件存储**: 本地文件存储系统，支持模型文件和缩略图
- **响应式设计**: 适配桌面和移动设备
- **Docker支持**: 完整的容器化部署方案

## 🛠️ 技术栈

### 前端
- React 19 + TypeScript
- Three.js + React Three Fiber
- Vite (构建工具)
- React Router (路由)
- Lucide React (图标)

### 后端
- Node.js + Express
- Prisma (ORM)
- PostgreSQL (数据库)
- Multer (文件上传)

### 部署
- Docker + Docker Compose
- Nginx (反向代理)
- 支持阿里云服务器部署

## 📋 系统要求

- Node.js 18+
- PostgreSQL 12+
- Docker (可选，用于容器化部署)

## 🔧 本地开发设置

### 1. 克隆项目
```bash
git clone <repository-url>
cd huitong-material
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
复制 `.env.example` 到 `.env` 并配置：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/huitong_material"

# 文件存储配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=50MB

# 服务器配置
PORT=3001
```

### 4. 设置数据库
```bash
# 创建数据库
createdb huitong_material

# 运行数据库迁移
npx prisma migrate dev

# 生成Prisma客户端
npx prisma generate
```

### 5. 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173 查看应用。

## 🐳 Docker部署

### 使用Docker Compose（推荐）
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 手动Docker部署
```bash
# 构建镜像
docker build -t huitong-material .

# 运行容器
docker run -d \
  --name huitong-app \
  -p 3000:3000 \
  -e DATABASE_URL="********************************/db" \
  huitong-material
```

## 🌐 阿里云服务器部署

### 1. 准备服务器
- Ubuntu 20.04+ 或 CentOS 8+
- 安装 Node.js, PostgreSQL, Nginx
- 配置防火墙开放 80, 443 端口

### 2. 使用部署脚本
```bash
# 上传项目文件到服务器
scp -r . user@your-server:/tmp/huitong-material

# 登录服务器
ssh user@your-server

# 运行部署脚本
cd /tmp/huitong-material
chmod +x deploy.sh
./deploy.sh
```

### 3. 配置域名和SSL
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com
```

## 📁 项目结构

```
huitong-material/
├── backend/                 # 后端代码
│   ├── middleware/         # 中间件
│   ├── utils/             # 工具函数
│   └── server.mjs         # 主服务器文件
├── src/                    # 前端代码
│   ├── components/        # React组件
│   ├── pages/            # 页面组件
│   ├── services/         # API服务
│   └── styles/           # 样式文件
├── prisma/                # 数据库配置
│   ├── schema.prisma     # 数据库模式
│   └── migrations/       # 数据库迁移
├── public/                # 静态资源
├── docker-compose.yml     # Docker Compose配置
├── Dockerfile            # Docker镜像配置
├── nginx.conf           # Nginx配置
└── deploy.sh           # 部署脚本
```

## 🔧 配置说明

### 环境变量
- `DATABASE_URL`: PostgreSQL数据库连接字符串
- `UPLOAD_DIR`: 文件上传目录路径
- `MAX_FILE_SIZE`: 最大文件上传大小
- `PORT`: 服务器端口

### 数据库模式
- `Model`: 3D模型信息
- `Material`: 材质信息

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `DATABASE_URL` 配置
   - 确保PostgreSQL服务运行
   - 验证数据库用户权限

2. **文件上传失败**
   - 检查 `UPLOAD_DIR` 权限
   - 验证 `MAX_FILE_SIZE` 设置
   - 确保磁盘空间充足

3. **3D模型加载失败**
   - 确认文件格式为GLB/GLTF
   - 检查文件大小限制
   - 验证文件路径正确

### 日志查看
```bash
# Docker部署
docker-compose logs -f app

# 系统服务部署
sudo journalctl -u huitong-material -f
```

## 📝 开发指南

### 添加新功能
1. 后端API: 在 `backend/server.mjs` 中添加路由
2. 前端组件: 在 `src/components/` 中创建组件
3. 数据库更改: 修改 `prisma/schema.prisma` 并运行迁移

### 代码规范
- 使用ESLint进行代码检查
- 遵循TypeScript类型定义
- 组件使用函数式写法

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！
