# API 基础 URL
VITE_API_BASE_URL=http://localhost:3001/api
VITE_SERVER_URL=http://localhost:3001

# 环境配置
VITE_ENV=development
NODE_ENV=development

# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/huitong_material"

# 文件存储配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=50MB

# 服务器配置
PORT=3001

# 生产环境配置示例
# VITE_API_BASE_URL=/api
# VITE_SERVER_URL=
# VITE_ENV=production
# DATABASE_URL="postgresql://username:password@localhost:5432/huitong_material_prod"
# UPLOAD_DIR=/var/www/uploads
# PORT=3000
