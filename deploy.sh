#!/bin/bash

# 部署脚本 - 用于阿里云服务器部署

set -e  # 遇到错误立即退出

echo "🚀 开始部署 Huitong Material 应用..."

# 检查是否为 root 用户
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  请不要以 root 用户运行此脚本"
    exit 1
fi

# 设置变量
APP_NAME="huitong-material"
APP_DIR="/opt/$APP_NAME"
BACKUP_DIR="/opt/backups/$APP_NAME"
NGINX_CONF="/etc/nginx/sites-available/$APP_NAME"
SYSTEMD_SERVICE="/etc/systemd/system/$APP_NAME.service"

# 创建备份
echo "📦 创建备份..."
sudo mkdir -p $BACKUP_DIR
if [ -d "$APP_DIR" ]; then
    sudo cp -r $APP_DIR $BACKUP_DIR/$(date +%Y%m%d_%H%M%S)
fi

# 停止现有服务
echo "⏹️  停止现有服务..."
sudo systemctl stop $APP_NAME || true
sudo systemctl stop nginx || true

# 创建应用目录
echo "📁 准备应用目录..."
sudo mkdir -p $APP_DIR
sudo chown $USER:$USER $APP_DIR

# 复制应用文件
echo "📋 复制应用文件..."
cp -r ./* $APP_DIR/
cd $APP_DIR

# 安装依赖
echo "📦 安装依赖..."
npm ci --only=production

# 生成 Prisma 客户端
echo "🔧 生成 Prisma 客户端..."
npx prisma generate

# 运行数据库迁移
echo "🗄️  运行数据库迁移..."
npx prisma migrate deploy

# 构建前端
echo "🏗️  构建前端..."
npm run build

# 创建上传目录
echo "📁 创建上传目录..."
sudo mkdir -p /var/www/uploads/{models,thumbnails,materials}
sudo chown -R $USER:www-data /var/www/uploads
sudo chmod -R 755 /var/www/uploads

# 创建 systemd 服务文件
echo "⚙️  配置系统服务..."
sudo tee $SYSTEMD_SERVICE > /dev/null <<EOF
[Unit]
Description=Huitong Material Application
After=network.target postgresql.service

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_DIR
Environment=NODE_ENV=production
EnvironmentFile=$APP_DIR/.env.production
ExecStart=/usr/bin/node backend/server.mjs
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 配置 Nginx
echo "🌐 配置 Nginx..."
sudo tee $NGINX_CONF > /dev/null <<EOF
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名

    client_max_body_size 50M;

    # 静态文件
    location /uploads/ {
        alias /var/www/uploads/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }

    # API 代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # 前端静态文件
    location / {
        root $APP_DIR/dist;
        try_files \$uri \$uri/ /index.html;
    }
}
EOF

# 启用 Nginx 站点
sudo ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
sudo nginx -t

# 重新加载 systemd 并启动服务
echo "🔄 启动服务..."
sudo systemctl daemon-reload
sudo systemctl enable $APP_NAME
sudo systemctl start $APP_NAME
sudo systemctl enable nginx
sudo systemctl start nginx

# 检查服务状态
echo "✅ 检查服务状态..."
sudo systemctl status $APP_NAME --no-pager
sudo systemctl status nginx --no-pager

echo "🎉 部署完成！"
echo "📝 请确保："
echo "   1. 更新 .env.production 文件中的数据库连接信息"
echo "   2. 更新 Nginx 配置中的域名"
echo "   3. 配置 SSL 证书（推荐使用 Let's Encrypt）"
echo "   4. 设置防火墙规则"
