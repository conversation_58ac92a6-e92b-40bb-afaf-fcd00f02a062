-- 初始化数据库脚本
-- 创建数据库（如果不存在）
-- CREATE DATABASE IF NOT EXISTS huitong_material;

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建扩展（如果需要）
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 这里可以添加初始数据
-- 例如：默认材质、示例模型等

-- 示例：插入一些默认材质
-- INSERT INTO "Material" (id, name, color, metalness, roughness, glass, "createdAt") VALUES
-- (uuid_generate_v4(), '默认金属', '#C0C0C0', 1.0, 0.1, 0.0, NOW()),
-- (uuid_generate_v4(), '默认塑料', '#FFFFFF', 0.0, 0.8, 0.0, NOW()),
-- (uuid_generate_v4(), '默认玻璃', '#E6F3FF', 0.0, 0.0, 1.0, NOW());

-- 创建索引以提高性能
-- CREATE INDEX IF NOT EXISTS idx_model_created_at ON "Model"("createdAt");
-- CREATE INDEX IF NOT EXISTS idx_material_created_at ON "Material"("createdAt");
